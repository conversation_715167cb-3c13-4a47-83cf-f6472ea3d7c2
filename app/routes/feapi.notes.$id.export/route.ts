// API URL: /feapi/notes/{id}/export
import { LoaderFunctionArgs } from "react-router";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, NoteApi } from "~/api/openapi/generated";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const noteId = params.id;

  if (!noteId) {
    throw new Response("Note ID is required", { status: 400 });
  }

  const configuration = new Configuration(
    await configurationParameters(request)
  );
  const noteApi = new NoteApi(configuration);

  // This API endpoint returns raw PDF data, with proper headers to trigger a file download, so we
  // return the raw result rather than using the generated API machinery to try to convert it into
  // JSON or text (which we do not want here).
  return (
    await noteApi.noteExportNote({
      noteId,
      exportNoteToPDFRequest: {},
    })
  ).raw;
};
